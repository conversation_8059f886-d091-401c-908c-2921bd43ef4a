import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { calculateDueDate } from '../lib/dueDate';
import { randomUUID } from 'crypto';
import { requireAuth, requireProjectCreator, requireProjectModifier, AuthenticatedRequest } from '../lib/auth';

const router = Router();
const prisma = new PrismaClient();

// Simple role helper using header. Default to internal if missing.
import type { Request } from 'express';
const isCustomer = (req: Request) => String(req.headers['x-user-role'] || '').toLowerCase() === 'customer';

// Helper: infer roles based on parentTaskId and status groupings
const isSalesTask = (p: { parentTaskId: string | null }): boolean => !p.parentTaskId;
const isDesignerTask = (p: { parentTaskId: string | null; status: string }): boolean => !!p.parentTaskId && ['designer_pending_assign','checklist','3d','2d','furniture_list','complete','designer_lost_deal'].includes(p.status);
const isSupervisorTask = (p: { status: string }): boolean => ['supervisor_pending_assign','created','inprogress','completed','floor_protection','plaster_ceiling','spc','first_painting','carpentry_measure','measure_others','carpentry_install','quartz_measure','quartz_install','glass_measure','glass_install','final_wiring','final_painting','install_others','plumbing','cleaning','defects','supervisor_lost_deal'].includes(p.status);

// Minimal event emitter (safe if TaskEvent table doesn't exist)
const emitEvent = async (data: {
  caseId: string;
  taskId: string;
  role: 'sales' | 'designer' | 'supervisor';
  type: string;
  actorUserId?: string | null;
  source?: string | null;
  payload?: unknown;
  occurredAt?: Date | string;
}) => {
  try {
    const id = randomUUID();

    // Create proper UTC timestamp
    const utcTimestamp = data.occurredAt ?
      new Date(data.occurredAt) :
      new Date();

    // Store the ISO string directly to ensure consistent UTC storage
    const utcIsoString = utcTimestamp.toISOString();

    console.log('Event timestamp info:');
    console.log('- UTC timestamp being stored:', utcIsoString);

    const payload = data.payload ? JSON.stringify(data.payload) : null;
    await prisma.$executeRawUnsafe(
      'INSERT INTO "TaskEvent" ("id","caseId","taskId","role","type","occurredAt","actorUserId","source","payload") VALUES ($1::uuid,$2::uuid,$3::uuid,$4,$5,$6::timestamp,$7,$8,$9::jsonb)',
      id,
      data.caseId,
      data.taskId,
      data.role,
      data.type,
      utcIsoString,
      data.actorUserId ?? null,
      data.source ?? 'api',
      payload,
    );
  } catch (e) {
    // Log errors but don't break primary flow
    console.error('Failed to emit event:', e);
  }
};

// Supervisor status list for guards
const SUPERVISOR_STATUSES = [
  'supervisor_pending_assign', 'created', 'inprogress', 'completed'
] as const;





const createProjectSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  client: z.string().min(1, 'Client is required'),
  salesAmount: z.number().int().nonnegative({ message: 'Sales amount must be a non-negative integer' }),
  assignedTo: z.string().optional(),
  // New project creation fields
  contact: z.string().regex(/^01[0-9]-\d{3} \d{4}$|^01[0-9]-\d{4} \d{4}$|^01[0-9]\d{7,8}$/, 'Invalid Malaysian mobile phone number format').optional(),
  projectDate: z.string().optional(),
  source: z.enum(['WS', 'FB', 'XHS', 'INSTA', 'Website']).optional(),
  vpDate: z.string().optional(),
  landed: z.boolean().optional(),
  remarks: z.string().optional(),
});

const updateStatusSchema = z.object({
  status: z.string(),
  salesSubStatus: z.string().optional(),
  isRevision: z.boolean().optional(),
  phaseCompletedAt: z.string().optional(), // ISO date string for supervisor subtask completion
  phaseKey: z.string().optional(), // which subtask is being completed
  isPhaseCompletion: z.boolean().optional(), // Flag to indicate this is just a phase completion, not status change
});

const updateProjectSchema = z.object({
  title: z.string().optional(),
  client: z.string().optional(),
  assignedTo: z.string().optional(),
  expiredDate: z.string().optional(),
  dueDate: z.string().optional(),
  supervisorSelectedPhases: z.array(z.string()).optional(),
  supervisorPhaseDates: z.record(z.string(), z.string()).optional(),
  comment: z.string().optional(), // renamed from remarks
  // Supervisor project-level fields
  renovationPeriod: z.string().optional(),
  handoverDate: z.string().optional(),
  supervisorProjectStartDate: z.string().optional(),
  // Appointment fields for sales
  appointment: z.enum(['online', 'site', 'showroom']).optional(),
  appointmentDate: z.string().optional(),
});

// Supervisor subtask transitions (strict)
const supervisorPhaseTransitionSchema = z.object({
  phaseKey: z.string(),
  to: z.enum(['not_started','booked','pending','in_progress','complete','defect']),
  at: z.string().optional(),
  contractor: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// Supervisor phase data update (without status change)
const supervisorPhaseUpdateSchema = z.object({
  phaseKey: z.string(),
  contractor: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

function allowedNextStatuses(current: string): string[] {
  // Allow users to select any status, no sequential restriction
  const allStatuses = ['not_started', 'booked', 'pending', 'in_progress', 'complete', 'defect'];
  // Return all statuses except the current one
  return allStatuses.filter(status => status !== current);
}

router.get('/', requireAuth, async (req: AuthenticatedRequest, res) => {
  const projects = await prisma.project.findMany({ orderBy: { createdAt: 'desc' } });

  if (isCustomer(req)) {
    // Mask sensitive fields for customers
    const masked = projects.map(p => ({ id: p.id, title: p.title, client: p.client, status: p.status, createdAt: p.createdAt, updatedAt: p.updatedAt }));
    return res.json(masked);
  }

  res.json(projects);
});

router.get('/:id', requireAuth, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const project = await prisma.project.findUnique({ where: { id } });
  if (!project) return res.status(404).json({ error: 'Not found' });

  if (isCustomer(req)) {
    const { id: pid, title, client, status, createdAt, updatedAt } = project;
    return res.json({ id: pid, title, client, status, createdAt, updatedAt });
  }

  res.json(project);
});

// Separate assignment endpoints
const assignSalesSchema = z.object({
  assignedTo: z.string(),
});

const assignDesignerSchema = z.object({
  assignedTo: z.string(),
});

router.patch('/:id/assign/sales', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parse = assignSalesSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  // Managers can assign any time; non-managers follow status restriction
  const role = req.user?.role || '';
  const isManager = role === 'manager';
  if (!isManager) {
    // Only allow when status is sales_pending_assign
    if (current.status !== 'sales_pending_assign') {
      return res.status(400).json({ error: 'Sales assignment allowed only in sales_pending_assign tasks' });
    }
  }

  // Move to lead status when assigned from sales_pending_assign
  const newStatus = current.status === 'sales_pending_assign' ? 'lead' : current.status;

  const updated = await prisma.project.update({
    where: { id },
    data: { assignedTo: parse.data.assignedTo, status: newStatus },
  });

  // Emit assignment change for Sales
  const actor = (req.headers['x-user-id'] as string) || null;
  try {
    await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'sales', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
  } catch {}

  res.json(updated);
});

router.patch('/:id/assign/designer', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parse = assignDesignerSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  // Owners and managers can assign any time; others follow status restriction
  const role = req.user?.role || '';
  const isOwnerOrManager = role === 'owner' || role === 'manager';
  if (!isOwnerOrManager) {
    // Only allow when status is designer_pending_assign (designer flow uses this endpoint)
    if (current.status !== 'designer_pending_assign') {
      // If this is a freshly created designer task via auto-flow and client is a bit out of sync,
      // fallback: allow assignment when current status is 'checklist' but no assignee yet
      if (!(current.status === 'checklist' && (!current.assignedTo || current.assignedTo === ''))) {
        return res.status(400).json({ error: 'Designer assignment allowed only in designer_pending_assign tasks' });
      }
    }
  }

  // Move to checklist if still at designer_pending_assign; otherwise keep at checklist
  const newStatus = current.status === 'designer_pending_assign' ? 'checklist' : current.status;

  const updated = await prisma.project.update({
    where: { id },
    data: { assignedTo: parse.data.assignedTo, status: newStatus },
  });

  // Emit assignment change for Designer
  const actor = (req.headers['x-user-id'] as string) || null;
  try {
    await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
  } catch {}

  res.json(updated);
});

const assignSupervisorSchema = z.object({
  assignedTo: z.string(),
});

router.patch('/:id/assign/supervisor', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parse = assignSupervisorSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  const role = req.user?.role || '';
  const isOwnerOrManager = role === 'owner' || role === 'manager';

  const isSupervisorPendingAssign = current.status === 'supervisor_pending_assign';
  const isSupervisorTaskUnassigned = SUPERVISOR_STATUSES.includes(current.status as (typeof SUPERVISOR_STATUSES)[number]) && current.status !== 'supervisor_pending_assign' && (!current.assignedTo || current.assignedTo === '');

  if (!isOwnerOrManager && !isSupervisorPendingAssign && !isSupervisorTaskUnassigned) {
    return res.status(400).json({ error: 'Supervisor assignment allowed only in supervisor_pending_assign or unassigned supervisor tasks' });
  }

  const data: { assignedTo: string; status?: string } = {
    assignedTo: parse.data.assignedTo,
  };
  if (isSupervisorPendingAssign) {
    data.status = 'created'; // On assignment, supervisor status becomes created (waiting for subtask selection)
  }

  const updated = await prisma.project.update({ where: { id }, data });

  // Emit supervisor assignment + selection events
  const actor = (req.headers['x-user-id'] as string) || null;
  try {
    await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'supervisor', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
  } catch {
    // ignore event errors
  }
  // Note: supervisor_task_assigned event will be emitted when subtasks are selected in the new endpoint

  res.json(updated);
});

const selectSupervisorSubtasksSchema = z.object({
  selectedTasks: z.array(z.string()).min(1),
});

router.patch('/:id/supervisor/subtasks', requireAuth, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parse = selectSupervisorSubtasksSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  // Only allow if project is in 'created' status and user is the assigned supervisor, manager, or owner
  const role = req.user?.role || '';
  const isOwnerOrManager = role === 'owner' || role === 'manager';
  const isAssignedSupervisor = current.assignedTo === req.user?.id;

  if (current.status !== 'created') {
    return res.status(400).json({ error: 'Subtask selection only allowed for projects in created status' });
  }

  if (!isOwnerOrManager && !isAssignedSupervisor) {
    return res.status(403).json({ error: 'Only assigned supervisor, manager, or owner can select subtasks' });
  }

  const data = {
    supervisorSelectedPhases: parse.data.selectedTasks,
    supervisorPhaseDates: {},
    status: 'inprogress', // Move to inprogress after subtask selection
  };

  const updated = await prisma.project.update({ where: { id }, data });

  // Emit subtask selection event
  const actor = (req.headers['x-user-id'] as string) || null;
  try {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'supervisor',
      type: 'supervisor_task_assigned',
      actorUserId: actor,
      source: 'api',
      payload: { selected: updated.supervisorSelectedPhases || [] }
    });
  } catch {
    // ignore event errors
  }

  res.json(updated);
});


router.post('/', requireAuth, requireProjectCreator, async (req: AuthenticatedRequest, res) => {
  const parse = createProjectSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const p = parse.data;
  const rootId = randomUUID();

  // Create project with sales_pending_assign status and no assignee
  const created = await prisma.project.create({
    data: {
      id: rootId,
      caseId: rootId,
      title: p.title,
      client: p.client,
      salesAmount: p.salesAmount,
      status: 'sales_pending_assign',
      assignedTo: null,
      // New project creation fields
      contact: p.contact,
      projectDate: p.projectDate ? new Date(p.projectDate) : undefined,
      source: p.source,
      vpDate: p.vpDate,
      landed: p.landed ?? false,
      remarks: p.remarks,
    }
  });

  // Emit task_created for Sales root
  const actor = req.userId || null;
  await emitEvent({ caseId: created.caseId, taskId: created.id, role: 'sales', type: 'task_created', actorUserId: actor, source: 'api', payload: { status: created.status } });

  res.status(201).json(created);
});

router.patch('/:id', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parse = updateProjectSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  const cast = parse.data as { title?: string; client?: string; assignedTo?: string; expiredDate?: string; dueDate?: string; supervisorSelectedPhases?: string[]; supervisorPhaseDates?: Record<string, string>; comment?: string; renovationPeriod?: string; handoverDate?: string; supervisorProjectStartDate?: string; appointment?: string; appointmentDate?: string };
  const data: { title?: string; client?: string; assignedTo?: string; expiredDate?: Date; dueDate?: Date; supervisorSelectedPhases?: string[]; supervisorPhaseDates?: Record<string, string>; status?: string; comment?: string; renovationPeriod?: string; handoverDate?: Date; supervisorProjectStartDate?: Date; appointment?: string; appointmentDate?: Date } = {
    title: cast.title,
    client: cast.client,
    assignedTo: cast.assignedTo,
    supervisorSelectedPhases: cast.supervisorSelectedPhases,
    supervisorPhaseDates: cast.supervisorPhaseDates,
    expiredDate: cast.expiredDate ? new Date(cast.expiredDate) : undefined,
    dueDate: cast.dueDate ? new Date(cast.dueDate) : undefined,
    comment: cast.comment,
    renovationPeriod: cast.renovationPeriod,
    handoverDate: cast.handoverDate ? new Date(cast.handoverDate) : undefined,
    supervisorProjectStartDate: cast.supervisorProjectStartDate ? new Date(cast.supervisorProjectStartDate) : undefined,
    appointment: cast.appointment,
    appointmentDate: cast.appointmentDate ? new Date(cast.appointmentDate) : undefined,
  };

  // Assignment rules:
  // - Sales: only when status === 'sales_pending_assign' (auto-progress to 'lead')
  // - Designer: only when status === 'designer_pending_assign' (auto-progress to 'checklist')
  // - Supervisor: only when status === 'supervisor_pending_assign' (auto-progress to 'floor_protection') or unassigned supervisor task
  if (data.assignedTo) {
    const isSalesPendingAssign = current.status === 'sales_pending_assign';
    const isDesignerPendingAssign = current.status === 'designer_pending_assign';
    const wantsSupervisorAssign = Array.isArray(data.supervisorSelectedPhases) && data.supervisorSelectedPhases.length > 0;

    // Disambiguate between different assignment types
    const isSalesAssign = isSalesPendingAssign;
    const isDesignerAssign = isDesignerPendingAssign && !wantsSupervisorAssign;
    const isSupervisorPendingAssign = current.status === 'supervisor_pending_assign' && wantsSupervisorAssign;

    const isSupervisorTask = SUPERVISOR_STATUSES.includes(current.status as (typeof SUPERVISOR_STATUSES)[number]) && current.status !== 'supervisor_pending_assign';
    const canAssignSupervisor = (isSupervisorPendingAssign || isSupervisorTask) && (!current.assignedTo || current.assignedTo === '');

    console.log('DEBUG Assignment Check:', {
      projectId: id,
      currentStatus: current.status,
      isSalesPendingAssign,
      isSalesAssign,
      isDesignerPendingAssign,
      isDesignerAssign,
      canAssignSupervisor,
      wantsSupervisorAssign,
      assignedTo: current.assignedTo,
      userRole: req.user?.role
    });

    if (!isSalesAssign && !isDesignerAssign && !canAssignSupervisor) {
      console.log('ASSIGNMENT BLOCKED - None of the conditions met');
      return res.status(400).json({ error: 'Assignment allowed only in sales_pending_assign, designer_pending_assign, supervisor_pending_assign or unassigned supervisor tasks' });
    }

    if (isSalesAssign) {
      // Sales assignment from sales_pending_assign -> lead
      data.status = 'lead';
    }

    if (isDesignerAssign) {
      // Designer assignment from designer_pending_assign -> checklist
      data.status = 'checklist';
    }

    if (isSupervisorPendingAssign) {
      // Supervisor assignment from supervisor_pending_assign -> floor_protection
      data.status = 'floor_protection';
    }

    // If assigning a supervisor task, require supervisorSelectedPhases
    if (isSupervisorPendingAssign || isSupervisorTask) {
      if (!data.supervisorSelectedPhases || data.supervisorSelectedPhases.length === 0) {
        return res.status(400).json({ error: 'Must provide supervisorSelectedPhases when assigning supervisor task' });
      }
      // Reset phase dates on (re-)assignment
      data.supervisorPhaseDates = {};
    }
  }

  // Due date update permission check - only managers can update due dates
  if (cast.dueDate !== undefined) {
    const role = req.user?.role || '';
    const isManager = role === 'manager';
    if (!isManager) {
      return res.status(403).json({ error: 'Only managers can update due dates' });
    }
  }

  const updated = await prisma.project.update({ where: { id }, data });

  // Sales per-update events: emit diffs for Sales tasks
  const actor = (req.headers['x-user-id'] as string) || null;
  if (isSalesTask(current)) {
    const changes: Array<{ field: string; from: unknown; to: unknown }> = [];
    const trackFields = ['title', 'client', 'salesAmount', 'assignedTo', 'salesSubStatus', 'expiredDate', 'appointment', 'appointmentDate'];
    for (const f of trackFields) {
      const fromVal = (current as Record<string, unknown>)[f];
      const toVal = (updated as Record<string, unknown>)[f];
      if (fromVal !== toVal) {
        changes.push({ field: f, from: fromVal ?? null, to: toVal ?? null });
      }
    }

    if (changes.length > 0) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'sales', type: 'sales_updated', actorUserId: actor, source: 'api', payload: { changes } });
    }

    // Assignment change event for Sales
    if (current.assignedTo !== updated.assignedTo) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'sales', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
    }
  }

  // Designer per-update events: capture general updates and explicit revisions
  if (isDesignerTask(current)) {
    const dChanges: Array<{ field: string; from: unknown; to: unknown }> = [];
    const dTrack = ['title','client','assignedTo','expiredDate','dueDate'];
    for (const f of dTrack) {
      const fromVal = (current as Record<string, unknown>)[f];
      const toVal = (updated as Record<string, unknown>)[f];
      if (fromVal !== toVal) {
        dChanges.push({ field: f, from: fromVal ?? null, to: toVal ?? null });
      }
    }

    if (dChanges.length > 0) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'designer_updated', actorUserId: actor, source: 'api', payload: { changes: dChanges } });
    }

    if (current.assignedTo !== updated.assignedTo) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
    }
  }

  // Supervisor per-update events: capture project-level field changes
  if (isSupervisorTask(current)) {
    const sChanges: Array<{ field: string; from: unknown; to: unknown }> = [];
    const sTrack = ['title', 'client', 'assignedTo', 'renovationPeriod', 'handoverDate', 'supervisorProjectStartDate'];
    for (const f of sTrack) {
      const fromVal = (current as Record<string, unknown>)[f];
      const toVal = (updated as Record<string, unknown>)[f];
      if (fromVal !== toVal) {
        sChanges.push({ field: f, from: fromVal ?? null, to: toVal ?? null });
      }
    }

    if (sChanges.length > 0) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'supervisor', type: 'supervisor_project_updated', actorUserId: actor, source: 'api', payload: { changes: sChanges } });
    }

    if (current.assignedTo !== updated.assignedTo) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'supervisor', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
    }
  }

  res.json(updated);
});

router.patch('/:id/status', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parse = updateStatusSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const { status, salesSubStatus, isRevision, isPhaseCompletion } = parse.data;
  let dueDate: string | undefined;
  let revisions3d: string[] | undefined;
  let revisions2d: string[] | undefined;

  const project = await prisma.project.findUnique({ where: { id } });
  const amount = project?.salesAmount ?? 0;

  if (status === '3d' || status === '2d') {
    dueDate = calculateDueDate(status as '3d' | '2d', amount, !!isRevision);
  }

  if (isRevision) {
    const now = new Date().toISOString();
    if (project) {
      if (status === '3d') revisions3d = [ ...(project.revisions3d ?? []), now ];
      if (status === '2d') revisions2d = [ ...(project.revisions2d ?? []), now ];
    }
  }

  const updated = await prisma.project.update({
    where: { id },
    data: {
      status,
      salesSubStatus,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      isRevision: isRevision ?? false,
      ...(revisions3d ? { revisions3d } : {}),
      ...(revisions2d ? { revisions2d } : {}),
    },
  });

  // Emit status change events for Sales/Designer/Supervisor
  const actor = (req.headers['x-user-id'] as string) || null;
  if (project && isSalesTask(project) && (project.status !== updated.status || project.salesSubStatus !== updated.salesSubStatus)) {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'sales',
      type: 'status_changed',
      actorUserId: actor,
      source: 'api',
      payload: { from: project.status, to: updated.status, salesSubStatusFrom: project.salesSubStatus ?? null, salesSubStatusTo: updated.salesSubStatus ?? null }
    });
  }
  if (project && isDesignerTask(project) && project.status !== updated.status) {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'designer',
      type: 'status_changed',
      actorUserId: actor,
      source: 'api',
      payload: { from: project.status, to: updated.status }
    });
  }
  if (project && isSupervisorTask(project) && project.status !== updated.status) {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'supervisor',
      type: 'status_changed',
      actorUserId: actor,
      source: 'api',
      payload: { from: project.status, to: updated.status }
    });
  }

  // Designer explicit revision events
  if (isDesignerTask(project || updated)) {
    if (isRevision) {
      const kind = status === '3d' ? '3d' : status === '2d' ? '2d' : undefined;
      if (kind) {
        await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'designer_revision', actorUserId: actor, source: 'api', payload: { kind } });
      }
    }
  }

  // Supervisor subtask completion tracking

  // Auto-create next role task when rules match
  // Rule 1: Sales reaches won_deal with subStatus '10%' -> create Designer designer_pending_assign
  if (status === 'won_deal' && salesSubStatus === '10%') {
    const caseId = project?.caseId ?? project?.id ?? randomUUID();
    // Avoid duplicate designer task for this source (parentTaskId=Sales task)
    const existingDesigner = await prisma.project.findFirst({ where: { caseId, status: 'designer_pending_assign', parentTaskId: project?.id ?? undefined } });
    if (!existingDesigner) {
      const createdDesigner = await prisma.project.create({
        data: {
          id: randomUUID(),
          title: project?.title || '',
          client: project?.client || '',
          salesAmount: project?.salesAmount ?? 0,
          status: 'designer_pending_assign',
          assignedTo: null,
          caseId,
          parentTaskId: project?.id ?? undefined,
        }
      });
      // Emit task_created for Designer child (not strictly Sales, but keeps timeline coherent)
      await emitEvent({ caseId, taskId: createdDesigner.id, role: 'designer', type: 'task_created', actorUserId: actor, source: 'api', payload: { status: createdDesigner.status } });
    }
  }

  // Rule 1b: Sales reaches won_deal with subStatus '3%'
  // Previously auto-marked as completed here. Change: do NOT auto-complete.
  // Completion should be an explicit action from the client (next click after 3%).

  // Cascade lost_deal to all tasks in the case with role-specific lost deal statuses
  if (status === 'lost_deal' || status === 'designer_lost_deal' || status === 'supervisor_lost_deal') {
    try {
      const allInCase = await prisma.project.findMany({ where: { caseId: updated.caseId } });
      for (const t of allInCase) {
        // Determine the appropriate lost deal status based on task role
        const role: 'sales' | 'designer' | 'supervisor' = isSalesTask(t) ? 'sales' : isDesignerTask(t) ? 'designer' : 'supervisor';
        const lostDealStatus = role === 'sales' ? 'lost_deal' : role === 'designer' ? 'designer_lost_deal' : 'supervisor_lost_deal';

        // Only update if not already in a lost deal state
        if (!['lost_deal', 'designer_lost_deal', 'supervisor_lost_deal'].includes(t.status)) {
          await prisma.project.update({ where: { id: t.id }, data: { status: lostDealStatus } });
          // Emit status_changed for the appropriate role
          await emitEvent({ caseId: t.caseId, taskId: t.id, role, type: 'status_changed', actorUserId: actor, source: 'api', payload: { from: t.status, to: lostDealStatus } });
        }
      }
    } catch (e) {
      // ignore cascade errors to avoid blocking primary update
    }
  }


  // Rule 2: Designer completes -> create Supervisor supervisor_pending_assign (unassigned)
  if (status === 'complete') {
    const caseId = project?.caseId ?? project?.id ?? randomUUID();
    // Avoid duplicate supervisor pending card for this designer task
    const existingSupervisor = await prisma.project.findFirst({ where: { caseId, status: 'supervisor_pending_assign', parentTaskId: project?.id ?? undefined } });
    if (!existingSupervisor) {
      const createdSupervisor = await prisma.project.create({
        data: {
          id: randomUUID(),
          title: project?.title || '',
          client: project?.client || '',
          salesAmount: project?.salesAmount ?? 0,
          status: 'supervisor_pending_assign',
          assignedTo: null,
          caseId,
          parentTaskId: project?.id ?? undefined,
        }
      });
      await emitEvent({ caseId, taskId: createdSupervisor.id, role: 'supervisor', type: 'task_created', actorUserId: actor, source: 'api', payload: { status: createdSupervisor.status } });
    }
  }

  // Supervisor subtask completion tracking
  if (SUPERVISOR_STATUSES.includes(status as (typeof SUPERVISOR_STATUSES)[number]) && req.body.phaseCompletedAt && req.body.phaseKey) {
    const p2 = await prisma.project.findUnique({ where: { id } });
    const phases = ((p2?.supervisorPhaseDates as unknown) || {}) as Record<string, string>;
    phases[req.body.phaseKey] = req.body.phaseCompletedAt;

    // Update subtask completion dates
    await prisma.project.update({ where: { id }, data: { supervisorPhaseDates: phases } });


    // Emit an event for this subtask completion
    await emitEvent({ caseId: p2!.caseId, taskId: p2!.id, role: 'supervisor', type: 'supervisor_task_completed', actorUserId: actor, source: 'api', payload: { phase: req.body.phaseKey, completedAt: req.body.phaseCompletedAt } });

    // If this is a subtask completion event, check if all selected tasks are completed
    if (isPhaseCompletion) {
      const refreshed = await prisma.project.findUnique({ where: { id } });
      const selected = (refreshed?.supervisorSelectedPhases as unknown as string[]) ?? [];
      const allDone = selected.length > 0 && selected.every((s) => (phases as Record<string, string>)[s]);

      if (allDone) {
        const completedProject = await prisma.project.update({ where: { id }, data: { status: 'completed' } });
        return res.json(completedProject);
      }

      return res.json(refreshed);
    }
  }

  res.json(updated);
});

// Transition endpoint for supervisor subtasks
router.post('/:id/supervisor-phase/transition', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parsed = supervisorPhaseTransitionSchema.safeParse(req.body);
  if (!parsed.success) return res.status(400).json({ error: parsed.error.flatten() });
  const { phaseKey, to, at, contractor, startDate, endDate } = parsed.data;

  const project = await prisma.project.findUnique({ where: { id } });
  if (!project) return res.status(404).json({ error: 'Not found' });

  const selected = (project.supervisorSelectedPhases as unknown as string[]) || [];
  if (!selected.includes(phaseKey)) return res.status(400).json({ error: 'Phase not selected for this project' });

  const states = ((project.supervisorPhaseStates as unknown) || {}) as Record<string, any>;
  const currentPhaseState = states[phaseKey] || {};
  const current = currentPhaseState.status ?? 'not_started';
  const allowed = allowedNextStatuses(current);
  if (!allowed.includes(to)) return res.status(400).json({ error: `Illegal transition from ${current} to ${to}` });

  const now = at ? new Date(at).toISOString() : new Date().toISOString();
  const patch: any = { status: to, updatedAt: now };
  if (to === 'booked' && !currentPhaseState.bookedAt) patch.bookedAt = now;
  if (to === 'in_progress' && !currentPhaseState.startedAt) patch.startedAt = now;
  if (to === 'complete' && !currentPhaseState.completedAt) patch.completedAt = now;
  if (to === 'defect') patch.defectedAt = now;
  if (current === 'defect' && to === 'in_progress') patch.defectResolvedAt = now;

  // Track changes for detailed logging
  const dataChanges: Array<{ field: string; from: any; to: any }> = [];

  // Add the new fields if provided and track changes
  if (contractor !== undefined && contractor !== currentPhaseState.contractor) {
    patch.contractor = contractor;
    dataChanges.push({
      field: 'contractor',
      from: currentPhaseState.contractor || null,
      to: contractor || null
    });
  }
  if (startDate !== undefined && startDate !== currentPhaseState.startDate) {
    patch.startDate = startDate;
    dataChanges.push({
      field: 'startDate',
      from: currentPhaseState.startDate || null,
      to: startDate || null
    });
  }
  if (endDate !== undefined && endDate !== currentPhaseState.endDate) {
    patch.endDate = endDate;
    dataChanges.push({
      field: 'endDate',
      from: currentPhaseState.endDate || null,
      to: endDate || null
    });
  }

  states[phaseKey] = { ...currentPhaseState, ...patch };

  const updated = await prisma.project.update({ where: { id }, data: { supervisorPhaseStates: states } });

  try {
    const actorUserId = (req.headers['x-user-id'] as string) || null;

    // Emit individual events for each change (status + each data field)
    // Status change event
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'supervisor',
      type: 'supervisor_phase_status_changed',
      actorUserId,
      source: 'api',
      payload: { phase: phaseKey, fromStatus: current, toStatus: to }
    });

    // Individual events for each data field change
    for (const change of dataChanges) {
      await emitEvent({
        caseId: updated.caseId,
        taskId: updated.id,
        role: 'supervisor',
        type: 'supervisor_phase_data_updated',
        actorUserId,
        source: 'api',
        payload: {
          phase: phaseKey,
          changes: [change] // Single change per event
        }
      });
    }
  } catch {}

  const allDone = selected.length > 0 && selected.every((s) => states[s]?.status === 'complete');
  if (allDone) {
    const completedProject = await prisma.project.update({ where: { id }, data: { status: 'completed' } });
    return res.json(completedProject);
  }

  return res.json(updated);
});

// Update supervisor phase data without changing status
router.patch('/:id/supervisor-phase/update', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const parsed = supervisorPhaseUpdateSchema.safeParse(req.body);
  if (!parsed.success) return res.status(400).json({ error: parsed.error.flatten() });
  const { phaseKey, contractor, startDate, endDate } = parsed.data;

  const project = await prisma.project.findUnique({ where: { id } });
  if (!project) return res.status(404).json({ error: 'Not found' });

  const selected = (project.supervisorSelectedPhases as unknown as string[]) || [];
  if (!selected.includes(phaseKey)) return res.status(400).json({ error: 'Phase not selected for this project' });

  const states = ((project.supervisorPhaseStates as unknown) || {}) as Record<string, any>;
  const currentPhaseState = states[phaseKey] || {};
  const now = new Date().toISOString();

  // Track changes for detailed logging
  const changes: Array<{ field: string; from: any; to: any }> = [];

  const patch: any = { updatedAt: now };

  if (contractor !== undefined && contractor !== currentPhaseState.contractor) {
    patch.contractor = contractor;
    changes.push({
      field: 'contractor',
      from: currentPhaseState.contractor || null,
      to: contractor || null
    });
  }

  if (startDate !== undefined && startDate !== currentPhaseState.startDate) {
    patch.startDate = startDate;
    changes.push({
      field: 'startDate',
      from: currentPhaseState.startDate || null,
      to: startDate || null
    });
  }

  if (endDate !== undefined && endDate !== currentPhaseState.endDate) {
    patch.endDate = endDate;
    changes.push({
      field: 'endDate',
      from: currentPhaseState.endDate || null,
      to: endDate || null
    });
  }

  // Only update if there are actual changes
  if (changes.length === 0) {
    return res.json(project);
  }

  states[phaseKey] = { ...currentPhaseState, ...patch };

  const updated = await prisma.project.update({ where: { id }, data: { supervisorPhaseStates: states } });

  try {
    const actorUserId = (req.headers['x-user-id'] as string) || null;

    // Emit individual events for each field change
    for (const change of changes) {
      await emitEvent({
        caseId: updated.caseId,
        taskId: updated.id,
        role: 'supervisor',
        type: 'supervisor_phase_data_updated',
        actorUserId,
        source: 'api',
        payload: {
          phase: phaseKey,
          changes: [change] // Single change per event
        }
      });
    }
  } catch {}

  return res.json(updated);
});

// Follow-up endpoint for lead status projects
router.post('/:id/follow-up', requireAuth, requireProjectModifier, async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const project = await prisma.project.findUnique({ where: { id } });
  if (!project) return res.status(404).json({ error: 'Project not found' });

  // Only allow follow-up for projects with "lead" status
  if (project.status !== 'lead') {
    return res.status(400).json({ error: 'Follow-up is only allowed for projects with lead status' });
  }

  // Check if maximum follow-ups (3) have been reached
  const currentFollowUps = project.followUpTimestamps || [];
  if (currentFollowUps.length >= 3) {
    return res.status(400).json({ error: 'Maximum of 3 follow-ups allowed' });
  }

  // Add current timestamp to follow-up array
  const now = new Date().toISOString();
  const updatedFollowUps = [...currentFollowUps, now];

  const updated = await prisma.project.update({
    where: { id },
    data: { followUpTimestamps: updatedFollowUps }
  });

  // Emit follow-up event
  const actor = (req.headers['x-user-id'] as string) || null;
  try {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'sales',
      type: 'lead_follow_up',
      actorUserId: actor,
      source: 'api',
      payload: {
        followUpCount: updatedFollowUps.length,
        timestamp: now
      }
    });
  } catch (error) {
    console.error('Failed to emit follow-up event:', error);
  }

  res.json(updated);
});

export default router;

